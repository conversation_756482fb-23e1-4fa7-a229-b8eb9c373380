#!/bin/bash

echo "🚀 Testing LM Studio Multi-Instance Setup..."
echo "============================================="

# Test Instance 1 (Coder - Port 1234)
echo "Testing Instance 1 (Coder) on port 1234..."
if curl -s http://127.0.0.1:1234/v1/models > /dev/null 2>&1; then
    MODEL1=$(curl -s http://127.0.0.1:1234/v1/models | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    echo "✅ Port 1234: $MODEL1"
else
    echo "❌ Port 1234: Not responding"
fi

# Test Instance 2 (Debugger - Port 1235)
echo "Testing Instance 2 (Debugger) on port 1235..."
if curl -s http://127.0.0.1:1235/v1/models > /dev/null 2>&1; then
    MODEL2=$(curl -s http://127.0.0.1:1235/v1/models | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    echo "✅ Port 1235: $MODEL2"
else
    echo "❌ Port 1235: Not responding"
fi

# Test Instance 3 (Reviewer - Port 1236)
echo "Testing Instance 3 (Reviewer) on port 1236..."
if curl -s http://127.0.0.1:1236/v1/models > /dev/null 2>&1; then
    MODEL3=$(curl -s http://127.0.0.1:1236/v1/models | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    echo "✅ Port 1236: $MODEL3"
else
    echo "❌ Port 1236: Not responding"
fi

echo "============================================="
echo "Setup complete! You can now use all 3 models in VS Code."
