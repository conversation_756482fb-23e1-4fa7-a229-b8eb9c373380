[tool.poetry]
name = "autogpt-libs"
version = "0.2.0"
description = "Shared libraries across NextGen AutoGPT"
authors = ["Aarushi <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "autogpt_libs" }]

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
colorama = "^0.4.6"
expiringdict = "^1.2.2"
google-cloud-logging = "^3.12.1"
pydantic = "^2.11.4"
pydantic-settings = "^2.9.1"
pyjwt = "^2.10.1"
pytest-asyncio = "^0.26.0"
pytest-mock = "^3.14.0"
supabase = "^2.15.1"

[tool.poetry.group.dev.dependencies]
redis = "^5.2.1"
ruff = "^0.11.10"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 88

[tool.ruff.lint]
extend-select = ["I"]  # sort dependencies
