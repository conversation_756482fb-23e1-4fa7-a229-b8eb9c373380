{"action": "synchronize", "number": 8358, "pull_request": {"url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls/8358", "id": 2128918491, "node_id": "PR_kwDOJKSTjM5-5Lfb", "html_url": "https://github.com/Significant-Gravitas/AutoGPT/pull/8358", "diff_url": "https://github.com/Significant-Gravitas/AutoGPT/pull/8358.diff", "patch_url": "https://github.com/Significant-Gravitas/AutoGPT/pull/8358.patch", "issue_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/8358", "number": 8358, "state": "open", "locked": false, "title": "feat(platform, blocks): Webhook-triggered blocks", "user": {"login": "<PERSON><PERSON><PERSON>", "id": 12185583, "node_id": "MDQ6VXNlcjEyMTg1NTgz", "avatar_url": "https://avatars.githubusercontent.com/u/12185583?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Pwuts", "html_url": "https://github.com/Pwuts", "followers_url": "https://api.github.com/users/Pwuts/followers", "following_url": "https://api.github.com/users/Pwuts/following{/other_user}", "gists_url": "https://api.github.com/users/Pwuts/gists{/gist_id}", "starred_url": "https://api.github.com/users/Pwuts/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Pwuts/subscriptions", "organizations_url": "https://api.github.com/users/Pwuts/orgs", "repos_url": "https://api.github.com/users/Pwuts/repos", "events_url": "https://api.github.com/users/Pwuts/events{/privacy}", "received_events_url": "https://api.github.com/users/Pwuts/received_events", "type": "User", "user_view_type": "public", "site_admin": false}, "body": "- Resolves #8352\r\n\r\n## Changes 🏗️\r\n\r\n- feat(blocks): Add GitHub Pull Request Trigger block\r\n\r\n### feat(platform): Add support for Webhook-triggered blocks\r\n- ⚠️ Add `PLATFORM_BASE_URL` setting\r\n\r\n- Add webhook config option and `BlockType.WEBHOOK` to `Block`\r\n  - Add check to `Block.__init__` to enforce type and shape of webhook event filter\r\n  - Add check to `Block.__init__` to enforce `payload` input on webhook blocks\r\n\r\n- Add `Webhook` model + CRUD functions in `backend.data.integrations` to represent webhooks created by our system\r\n  - Add `IntegrationWebhook` to DB schema + reference `AgentGraphNode.webhook_id`\r\n    - Add `set_node_webhook(..)` in `backend.data.graph`\r\n\r\n- Add webhook-related endpoints:\r\n  - `POST /integrations/{provider}/webhooks/{webhook_id}/ingress` endpoint, to receive webhook payloads, and for all associated nodes create graph executions\r\n    - Add `Node.is_triggered_by_event_type(..)` helper method\r\n  - `POST /integrations/{provider}/webhooks/{webhook_id}/ping` endpoint, to allow testing a webhook\r\n  - Add `WebhookEvent` + pub/sub functions in `backend.data.integrations`\r\n\r\n- Add `backend.integrations.webhooks` module, including:\r\n  - `graph_lifecycle_hooks`, e.g. `on_graph_activate(..)`, to handle corresponding webhook creation etc.\r\n    - Add calls to these hooks in the graph create/update endpoints\r\n  - `BaseWebhooksManager` + `GithubWebhooksManager` to handle creating + registering, removing + deregistering, and retrieving existing webhooks, and validating incoming payloads\r\n\r\n### Other improvements\r\n- fix(blocks): Allow having an input and output pin with the same name\r\n- feat(blocks): Allow hiding inputs (e.g. `payload`) with `SchemaField(hidden=True)`\r\n- feat(backend/data): Add `graph_id`, `graph_version` to `Node`; `user_id` to `GraphMeta`\r\n  - Add `Creatable` versions of `Node`, `GraphMeta` and `Graph` without these properties\r\n  - Add `graph_from_creatable(..)` helper function in `backend.data.graph`\r\n- refactor(backend/data): Make `RedisEventQueue` generic\r\n- refactor(frontend): Deduplicate & clean up code for different block types in `generateInputHandles(..)` in `CustomNode`\r\n- refactor(backend): Remove unused subgraph functionality\r\n\r\n## How it works\r\n- When a graph is created, the `on_graph_activate` and `on_node_activate` hooks are called on the graph and its nodes\r\n- If a webhook-triggered node has presets for all the relevant inputs, `on_node_activate` will get/create a suitable webhook and link it by setting `AgentGraphNode.webhook_id`\r\n  - `on_node_activate` uses `webhook_manager.get_suitable_webhook(..)`, which tries to find a suitable webhook (with matching requirements) or creates it if none exists yet\r\n- When a graph is deactivated (in favor of a newer/other version) or deleted, `on_graph_deactivate` and `on_node_deactivate` are called on the graph and its nodes to clean up webhooks that are no longer in use\r\n- When a valid webhook payload is received, two things happen:\r\n  1. It is broadcast on the Redis channel `webhooks/{webhook_id}/{event_type}`\r\n  2. Graph executions are initiated for all nodes triggered by this webhook\r\n\r\n## TODO\r\n- [ ] #8537\r\n- [x] #8538\r\n- [ ] #8357\r\n- [ ] ~~#8554~~ can be done in a follow-up PR\r\n- [ ] Test test test!\r\n- [ ] Add note on `repo` input of webhook blocks that the credentials used must have the right permissions for the given organization/repo\r\n- [x] Implement proper detection and graceful handling of webhook creation failing due to insufficient permissions. This should give a clear message to the user to e.g. \"give the app access to this organization in your settings\".\r\n- [ ] Nice-to-have: make a button on webhook blocks to trigger a ping and check its result. The API endpoints for this is already implemented.", "created_at": "2024-10-16T22:13:47Z", "updated_at": "2024-11-11T18:34:54Z", "closed_at": null, "merged_at": null, "merge_commit_sha": "cbfd0cdd8db52cdd5a3b7ce088fc0ab4617a652e", "assignee": {"login": "<PERSON><PERSON><PERSON>", "id": 12185583, "node_id": "MDQ6VXNlcjEyMTg1NTgz", "avatar_url": "https://avatars.githubusercontent.com/u/12185583?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Pwuts", "html_url": "https://github.com/Pwuts", "followers_url": "https://api.github.com/users/Pwuts/followers", "following_url": "https://api.github.com/users/Pwuts/following{/other_user}", "gists_url": "https://api.github.com/users/Pwuts/gists{/gist_id}", "starred_url": "https://api.github.com/users/Pwuts/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Pwuts/subscriptions", "organizations_url": "https://api.github.com/users/Pwuts/orgs", "repos_url": "https://api.github.com/users/Pwuts/repos", "events_url": "https://api.github.com/users/Pwuts/events{/privacy}", "received_events_url": "https://api.github.com/users/Pwuts/received_events", "type": "User", "user_view_type": "public", "site_admin": false}, "assignees": [{"login": "<PERSON><PERSON><PERSON>", "id": 12185583, "node_id": "MDQ6VXNlcjEyMTg1NTgz", "avatar_url": "https://avatars.githubusercontent.com/u/12185583?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Pwuts", "html_url": "https://github.com/Pwuts", "followers_url": "https://api.github.com/users/Pwuts/followers", "following_url": "https://api.github.com/users/Pwuts/following{/other_user}", "gists_url": "https://api.github.com/users/Pwuts/gists{/gist_id}", "starred_url": "https://api.github.com/users/Pwuts/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Pwuts/subscriptions", "organizations_url": "https://api.github.com/users/Pwuts/orgs", "repos_url": "https://api.github.com/users/Pwuts/repos", "events_url": "https://api.github.com/users/Pwuts/events{/privacy}", "received_events_url": "https://api.github.com/users/Pwuts/received_events", "type": "User", "user_view_type": "public", "site_admin": false}], "requested_reviewers": [{"login": "kcze", "id": 34861343, "node_id": "MDQ6VXNlcjM0ODYxMzQz", "avatar_url": "https://avatars.githubusercontent.com/u/34861343?v=4", "gravatar_id": "", "url": "https://api.github.com/users/kcze", "html_url": "https://github.com/kcze", "followers_url": "https://api.github.com/users/kcze/followers", "following_url": "https://api.github.com/users/kcze/following{/other_user}", "gists_url": "https://api.github.com/users/kcze/gists{/gist_id}", "starred_url": "https://api.github.com/users/kcze/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/kcze/subscriptions", "organizations_url": "https://api.github.com/users/kcze/orgs", "repos_url": "https://api.github.com/users/kcze/repos", "events_url": "https://api.github.com/users/kcze/events{/privacy}", "received_events_url": "https://api.github.com/users/kcze/received_events", "type": "User", "user_view_type": "public", "site_admin": false}], "requested_teams": [{"name": "DevOps", "id": 9547361, "node_id": "T_kwDOB8roIc4Aka5h", "slug": "devops", "description": "", "privacy": "closed", "notification_setting": "notifications_enabled", "url": "https://api.github.com/organizations/130738209/team/9547361", "html_url": "https://github.com/orgs/Significant-Gravitas/teams/devops", "members_url": "https://api.github.com/organizations/130738209/team/9547361/members{/member}", "repositories_url": "https://api.github.com/organizations/130738209/team/9547361/repos", "permission": "pull", "parent": null}], "labels": [{"id": 5272676214, "node_id": "LA_kwDOJKSTjM8AAAABOkandg", "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels/documentation", "name": "documentation", "color": "0075ca", "default": true, "description": "Improvements or additions to documentation"}, {"id": 5410633769, "node_id": "LA_kwDOJKSTjM8AAAABQn-4KQ", "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels/size/xl", "name": "size/xl", "color": "E751DD", "default": false, "description": ""}, {"id": 6892322271, "node_id": "LA_kwDOJKSTjM8AAAABmtB93w", "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels/Review%20effort%20[1-5]:%204", "name": "Review effort [1-5]: 4", "color": "d1bcf9", "default": false, "description": null}, {"id": 7218433025, "node_id": "LA_kwDOJKSTjM8AAAABrkCMAQ", "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels/platform/frontend", "name": "platform/frontend", "color": "033C07", "default": false, "description": "AutoGPT Platform - Front end"}, {"id": 7219356193, "node_id": "LA_kwDOJKSTjM8AAAABrk6iIQ", "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels/platform/backend", "name": "platform/backend", "color": "ededed", "default": false, "description": "AutoGPT Platform - Back end"}, {"id": 7515330106, "node_id": "LA_kwDOJKSTjM8AAAABv_LWOg", "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels/platform/blocks", "name": "platform/blocks", "color": "eb5757", "default": false, "description": null}], "milestone": null, "draft": false, "commits_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls/8358/commits", "review_comments_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls/8358/comments", "review_comment_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls/comments{/number}", "comments_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/8358/comments", "statuses_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/statuses/8f708a2b60463eec10747d8f45dead35b5a45bd0", "head": {"label": "Significant-Gravitas:reinier/open-1961-implement-github-on-pull-request-block", "ref": "reinier/open-1961-implement-github-on-pull-request-block", "sha": "8f708a2b60463eec10747d8f45dead35b5a45bd0", "user": {"login": "Significant-Gravitas", "id": 130738209, "node_id": "O_kgDOB8roIQ", "avatar_url": "https://avatars.githubusercontent.com/u/130738209?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Significant-Gravitas", "html_url": "https://github.com/Significant-Gravitas", "followers_url": "https://api.github.com/users/Significant-Gravitas/followers", "following_url": "https://api.github.com/users/Significant-Gravitas/following{/other_user}", "gists_url": "https://api.github.com/users/Significant-Gravitas/gists{/gist_id}", "starred_url": "https://api.github.com/users/Significant-Gravitas/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Significant-Gravitas/subscriptions", "organizations_url": "https://api.github.com/users/Significant-Gravitas/orgs", "repos_url": "https://api.github.com/users/Significant-Gravitas/repos", "events_url": "https://api.github.com/users/Significant-Gravitas/events{/privacy}", "received_events_url": "https://api.github.com/users/Significant-Gravitas/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "repo": {"id": 614765452, "node_id": "R_kgDOJKSTjA", "name": "AutoGPT", "full_name": "Significant-Gravitas/AutoGPT", "private": false, "owner": {"login": "Significant-Gravitas", "id": 130738209, "node_id": "O_kgDOB8roIQ", "avatar_url": "https://avatars.githubusercontent.com/u/130738209?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Significant-Gravitas", "html_url": "https://github.com/Significant-Gravitas", "followers_url": "https://api.github.com/users/Significant-Gravitas/followers", "following_url": "https://api.github.com/users/Significant-Gravitas/following{/other_user}", "gists_url": "https://api.github.com/users/Significant-Gravitas/gists{/gist_id}", "starred_url": "https://api.github.com/users/Significant-Gravitas/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Significant-Gravitas/subscriptions", "organizations_url": "https://api.github.com/users/Significant-Gravitas/orgs", "repos_url": "https://api.github.com/users/Significant-Gravitas/repos", "events_url": "https://api.github.com/users/Significant-Gravitas/events{/privacy}", "received_events_url": "https://api.github.com/users/Significant-Gravitas/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "html_url": "https://github.com/Significant-Gravitas/AutoGPT", "description": "AutoGPT is the vision of accessible AI for everyone, to use and to build on. Our mission is to provide the tools, so that you can focus on what matters.", "fork": false, "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT", "forks_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/forks", "keys_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/teams", "hooks_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/hooks", "issue_events_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/events{/number}", "events_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/events", "assignees_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/assignees{/user}", "branches_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/branches{/branch}", "tags_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/tags", "blobs_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/refs{/sha}", "trees_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/statuses/{sha}", "languages_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/languages", "stargazers_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/stargazers", "contributors_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/contributors", "subscribers_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/subscribers", "subscription_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/subscription", "commits_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/commits{/sha}", "git_commits_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/commits{/sha}", "comments_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/comments{/number}", "issue_comment_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/comments{/number}", "contents_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/contents/{+path}", "compare_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/merges", "archive_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/downloads", "issues_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues{/number}", "pulls_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls{/number}", "milestones_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/milestones{/number}", "notifications_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels{/name}", "releases_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/releases{/id}", "deployments_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/deployments", "created_at": "2023-03-16T09:21:07Z", "updated_at": "2024-11-11T18:16:29Z", "pushed_at": "2024-11-11T18:34:52Z", "git_url": "git://github.com/Significant-Gravitas/AutoGPT.git", "ssh_url": "**************:Significant-Gravitas/AutoGPT.git", "clone_url": "https://github.com/Significant-Gravitas/AutoGPT.git", "svn_url": "https://github.com/Significant-Gravitas/AutoGPT", "homepage": "https://agpt.co", "size": 181894, "stargazers_count": 168203, "watchers_count": 168203, "language": "Python", "has_issues": true, "has_projects": true, "has_downloads": true, "has_wiki": true, "has_pages": false, "has_discussions": true, "forks_count": 44376, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 189, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": ["ai", "artificial-intelligence", "autonomous-agents", "gpt-4", "openai", "python"], "visibility": "public", "forks": 44376, "open_issues": 189, "watchers": 168203, "default_branch": "master", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": false, "allow_auto_merge": true, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "COMMIT_MESSAGES", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "BLANK", "merge_commit_title": "PR_TITLE"}}, "base": {"label": "Significant-Gravitas:dev", "ref": "dev", "sha": "0b5b95eff5e18c1e162d2b30b66a7be2bed1cbc2", "user": {"login": "Significant-Gravitas", "id": 130738209, "node_id": "O_kgDOB8roIQ", "avatar_url": "https://avatars.githubusercontent.com/u/130738209?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Significant-Gravitas", "html_url": "https://github.com/Significant-Gravitas", "followers_url": "https://api.github.com/users/Significant-Gravitas/followers", "following_url": "https://api.github.com/users/Significant-Gravitas/following{/other_user}", "gists_url": "https://api.github.com/users/Significant-Gravitas/gists{/gist_id}", "starred_url": "https://api.github.com/users/Significant-Gravitas/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Significant-Gravitas/subscriptions", "organizations_url": "https://api.github.com/users/Significant-Gravitas/orgs", "repos_url": "https://api.github.com/users/Significant-Gravitas/repos", "events_url": "https://api.github.com/users/Significant-Gravitas/events{/privacy}", "received_events_url": "https://api.github.com/users/Significant-Gravitas/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "repo": {"id": 614765452, "node_id": "R_kgDOJKSTjA", "name": "AutoGPT", "full_name": "Significant-Gravitas/AutoGPT", "private": false, "owner": {"login": "Significant-Gravitas", "id": 130738209, "node_id": "O_kgDOB8roIQ", "avatar_url": "https://avatars.githubusercontent.com/u/130738209?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Significant-Gravitas", "html_url": "https://github.com/Significant-Gravitas", "followers_url": "https://api.github.com/users/Significant-Gravitas/followers", "following_url": "https://api.github.com/users/Significant-Gravitas/following{/other_user}", "gists_url": "https://api.github.com/users/Significant-Gravitas/gists{/gist_id}", "starred_url": "https://api.github.com/users/Significant-Gravitas/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Significant-Gravitas/subscriptions", "organizations_url": "https://api.github.com/users/Significant-Gravitas/orgs", "repos_url": "https://api.github.com/users/Significant-Gravitas/repos", "events_url": "https://api.github.com/users/Significant-Gravitas/events{/privacy}", "received_events_url": "https://api.github.com/users/Significant-Gravitas/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "html_url": "https://github.com/Significant-Gravitas/AutoGPT", "description": "AutoGPT is the vision of accessible AI for everyone, to use and to build on. Our mission is to provide the tools, so that you can focus on what matters.", "fork": false, "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT", "forks_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/forks", "keys_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/teams", "hooks_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/hooks", "issue_events_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/events{/number}", "events_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/events", "assignees_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/assignees{/user}", "branches_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/branches{/branch}", "tags_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/tags", "blobs_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/refs{/sha}", "trees_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/statuses/{sha}", "languages_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/languages", "stargazers_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/stargazers", "contributors_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/contributors", "subscribers_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/subscribers", "subscription_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/subscription", "commits_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/commits{/sha}", "git_commits_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/commits{/sha}", "comments_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/comments{/number}", "issue_comment_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/comments{/number}", "contents_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/contents/{+path}", "compare_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/merges", "archive_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/downloads", "issues_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues{/number}", "pulls_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls{/number}", "milestones_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/milestones{/number}", "notifications_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels{/name}", "releases_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/releases{/id}", "deployments_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/deployments", "created_at": "2023-03-16T09:21:07Z", "updated_at": "2024-11-11T18:16:29Z", "pushed_at": "2024-11-11T18:34:52Z", "git_url": "git://github.com/Significant-Gravitas/AutoGPT.git", "ssh_url": "**************:Significant-Gravitas/AutoGPT.git", "clone_url": "https://github.com/Significant-Gravitas/AutoGPT.git", "svn_url": "https://github.com/Significant-Gravitas/AutoGPT", "homepage": "https://agpt.co", "size": 181894, "stargazers_count": 168203, "watchers_count": 168203, "language": "Python", "has_issues": true, "has_projects": true, "has_downloads": true, "has_wiki": true, "has_pages": false, "has_discussions": true, "forks_count": 44376, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 189, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": ["ai", "artificial-intelligence", "autonomous-agents", "gpt-4", "openai", "python"], "visibility": "public", "forks": 44376, "open_issues": 189, "watchers": 168203, "default_branch": "master", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": false, "allow_auto_merge": true, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "COMMIT_MESSAGES", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "BLANK", "merge_commit_title": "PR_TITLE"}}, "_links": {"self": {"href": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls/8358"}, "html": {"href": "https://github.com/Significant-Gravitas/AutoGPT/pull/8358"}, "issue": {"href": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/8358"}, "comments": {"href": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/8358/comments"}, "review_comments": {"href": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls/8358/comments"}, "review_comment": {"href": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls/comments{/number}"}, "commits": {"href": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls/8358/commits"}, "statuses": {"href": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/statuses/8f708a2b60463eec10747d8f45dead35b5a45bd0"}}, "author_association": "MEMBER", "auto_merge": null, "active_lock_reason": null, "merged": false, "mergeable": null, "rebaseable": null, "mergeable_state": "unknown", "merged_by": null, "comments": 12, "review_comments": 29, "maintainer_can_modify": false, "commits": 62, "additions": 1674, "deletions": 331, "changed_files": 36}, "before": "f40aef87672203f47bbbd53f83fae0964c5624da", "after": "8f708a2b60463eec10747d8f45dead35b5a45bd0", "repository": {"id": 614765452, "node_id": "R_kgDOJKSTjA", "name": "AutoGPT", "full_name": "Significant-Gravitas/AutoGPT", "private": false, "owner": {"login": "Significant-Gravitas", "id": 130738209, "node_id": "O_kgDOB8roIQ", "avatar_url": "https://avatars.githubusercontent.com/u/130738209?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Significant-Gravitas", "html_url": "https://github.com/Significant-Gravitas", "followers_url": "https://api.github.com/users/Significant-Gravitas/followers", "following_url": "https://api.github.com/users/Significant-Gravitas/following{/other_user}", "gists_url": "https://api.github.com/users/Significant-Gravitas/gists{/gist_id}", "starred_url": "https://api.github.com/users/Significant-Gravitas/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Significant-Gravitas/subscriptions", "organizations_url": "https://api.github.com/users/Significant-Gravitas/orgs", "repos_url": "https://api.github.com/users/Significant-Gravitas/repos", "events_url": "https://api.github.com/users/Significant-Gravitas/events{/privacy}", "received_events_url": "https://api.github.com/users/Significant-Gravitas/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "html_url": "https://github.com/Significant-Gravitas/AutoGPT", "description": "AutoGPT is the vision of accessible AI for everyone, to use and to build on. Our mission is to provide the tools, so that you can focus on what matters.", "fork": false, "url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT", "forks_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/forks", "keys_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/teams", "hooks_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/hooks", "issue_events_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/events{/number}", "events_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/events", "assignees_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/assignees{/user}", "branches_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/branches{/branch}", "tags_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/tags", "blobs_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/refs{/sha}", "trees_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/statuses/{sha}", "languages_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/languages", "stargazers_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/stargazers", "contributors_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/contributors", "subscribers_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/subscribers", "subscription_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/subscription", "commits_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/commits{/sha}", "git_commits_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/git/commits{/sha}", "comments_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/comments{/number}", "issue_comment_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues/comments{/number}", "contents_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/contents/{+path}", "compare_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/merges", "archive_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/downloads", "issues_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/issues{/number}", "pulls_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/pulls{/number}", "milestones_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/milestones{/number}", "notifications_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/labels{/name}", "releases_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/releases{/id}", "deployments_url": "https://api.github.com/repos/Significant-Gravitas/AutoGPT/deployments", "created_at": "2023-03-16T09:21:07Z", "updated_at": "2024-11-11T18:16:29Z", "pushed_at": "2024-11-11T18:34:52Z", "git_url": "git://github.com/Significant-Gravitas/AutoGPT.git", "ssh_url": "**************:Significant-Gravitas/AutoGPT.git", "clone_url": "https://github.com/Significant-Gravitas/AutoGPT.git", "svn_url": "https://github.com/Significant-Gravitas/AutoGPT", "homepage": "https://agpt.co", "size": 181894, "stargazers_count": 168203, "watchers_count": 168203, "language": "Python", "has_issues": true, "has_projects": true, "has_downloads": true, "has_wiki": true, "has_pages": false, "has_discussions": true, "forks_count": 44376, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 189, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": ["ai", "artificial-intelligence", "autonomous-agents", "gpt-4", "openai", "python"], "visibility": "public", "forks": 44376, "open_issues": 189, "watchers": 168203, "default_branch": "master", "custom_properties": {}}, "organization": {"login": "Significant-Gravitas", "id": 130738209, "node_id": "O_kgDOB8roIQ", "url": "https://api.github.com/orgs/Significant-Gravitas", "repos_url": "https://api.github.com/orgs/Significant-Gravitas/repos", "events_url": "https://api.github.com/orgs/Significant-Gravitas/events", "hooks_url": "https://api.github.com/orgs/Significant-Gravitas/hooks", "issues_url": "https://api.github.com/orgs/Significant-Gravitas/issues", "members_url": "https://api.github.com/orgs/Significant-Gravitas/members{/member}", "public_members_url": "https://api.github.com/orgs/Significant-Gravitas/public_members{/member}", "avatar_url": "https://avatars.githubusercontent.com/u/130738209?v=4", "description": ""}, "enterprise": {"id": 149607, "slug": "significant-gravitas", "name": "Significant Gravitas", "node_id": "E_kgDOAAJIZw", "avatar_url": "https://avatars.githubusercontent.com/b/149607?v=4", "description": "The creators of AutoGPT", "website_url": "discord.gg/autogpt", "html_url": "https://github.com/enterprises/significant-gravitas", "created_at": "2024-04-18T17:43:53Z", "updated_at": "2024-10-23T16:59:55Z"}, "sender": {"login": "<PERSON><PERSON><PERSON>", "id": 12185583, "node_id": "MDQ6VXNlcjEyMTg1NTgz", "avatar_url": "https://avatars.githubusercontent.com/u/12185583?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Pwuts", "html_url": "https://github.com/Pwuts", "followers_url": "https://api.github.com/users/Pwuts/followers", "following_url": "https://api.github.com/users/Pwuts/following{/other_user}", "gists_url": "https://api.github.com/users/Pwuts/gists{/gist_id}", "starred_url": "https://api.github.com/users/Pwuts/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Pwuts/subscriptions", "organizations_url": "https://api.github.com/users/Pwuts/orgs", "repos_url": "https://api.github.com/users/Pwuts/repos", "events_url": "https://api.github.com/users/Pwuts/events{/privacy}", "received_events_url": "https://api.github.com/users/Pwuts/received_events", "type": "User", "user_view_type": "public", "site_admin": false}}