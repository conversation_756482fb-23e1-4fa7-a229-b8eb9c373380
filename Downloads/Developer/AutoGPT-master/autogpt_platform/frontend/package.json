{"name": "frontend", "version": "0.3.4", "private": true, "scripts": {"dev": "next dev", "dev:test": "NODE_ENV=test && next dev --turbo", "build": "SKIP_STORYBOOK_TESTS=true next build", "start": "next start", "start:standalone": "cd .next/standalone && node server.js", "lint": "next lint && prettier --check .", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "next build --turbo && playwright test", "test-ui": "next build --turbo && playwright test --ui", "test:no-build": "playwright test", "gentests": "playwright codegen http://localhost:3000", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook", "test-storybook:ci": "concurrently -k -s first -n \"SB,TEST\" -c \"magenta,blue\" \"pnpm run build-storybook -- --quiet && npx http-server storybook-static --port 6006 --silent\" \"wait-on tcp:6006 && pnpm run test-storybook\""}, "browserslist": ["defaults"], "dependencies": {"@faker-js/faker": "^9.8.0", "@hookform/resolvers": "^3.10.0", "@next/third-parties": "^15.3.2", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@sentry/nextjs": "^9", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "@tanstack/react-table": "^8.21.3", "@types/jaro-winkler": "^0.2.4", "@xyflow/react": "12.6.4", "ajv": "^8.17.1", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "cookie": "1.0.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "elliptic": "6.6.1", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.16.0", "geist": "^1.4.2", "jaro-winkler": "^0.2.8", "launchdarkly-react-client-sdk": "^3.7.0", "lodash": "^4.17.21", "lucide-react": "^0.513.0", "moment": "^2.30.1", "next": "^15.3.2", "next-themes": "^0.4.6", "party-js": "^2.2.0", "react": "^18", "react-day-picker": "^9.7.0", "react-dom": "^18", "react-drag-drop-files": "^2.4.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-markdown": "^9.0.3", "react-modal": "^3.16.3", "react-shepherd": "^6.1.8", "recharts": "^2.15.3", "shepherd.js": "^14.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.51", "@tanstack/react-query": "^5.51.11", "react-remove-scroll-bar": "^2.3.8", "react-style-singleton": "^2.2.1", "resolve": "^1.22.4", "use-callback-ref": "^1.3.0"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@playwright/test": "^1.50.1", "@storybook/addon-a11y": "^8.5.3", "@storybook/addon-essentials": "^8.5.3", "@storybook/addon-interactions": "^8.5.3", "@storybook/addon-links": "^8.5.3", "@storybook/addon-onboarding": "^8.5.3", "@storybook/blocks": "^8.5.3", "@storybook/nextjs": "^8.5.3", "@storybook/react": "^8.3.5", "@storybook/test": "^8.3.5", "@storybook/test-runner": "^0.22.0", "@types/canvas-confetti": "^1.9.0", "@types/lodash": "^4.17.17", "@types/negotiator": "^0.6.3", "@types/node": "^22.13.0", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "axe-playwright": "^2.0.3", "chromatic": "^11.25.2", "concurrently": "^9.1.2", "eslint": "^8", "eslint-config-next": "15.3.3", "eslint-plugin-storybook": "^0.11.2", "msw": "^2.9.0", "msw-storybook-addon": "^2.0.3", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "storybook": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}, "msw": {"workerDirectory": ["public"]}, "packageManager": "pnpm@10.11.1+sha256.211e9990148495c9fc30b7e58396f7eeda83d9243eb75407ea4f8650fb161f7c"}