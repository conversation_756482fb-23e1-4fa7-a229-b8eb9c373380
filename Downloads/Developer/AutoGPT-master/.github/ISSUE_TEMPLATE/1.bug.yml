name: Bug report 🐛
description: Create a bug report for AutoGPT.
labels: ['status: needs triage']
body:
  - type: markdown
    attributes:
      value: |
        ### ⚠️ Before you continue
        * Check out our [backlog], [roadmap] and join our [discord] to discuss what's going on
        * If you need help, you can ask in the [discussions] section or in [#tech-support]
        * **<PERSON><PERSON><PERSON> search the [existing issues] before creating a new one**
        * Read our [wiki page on Contributing]
        [backlog]: https://github.com/orgs/Significant-Gravitas/projects/1
        [roadmap]: https://github.com/orgs/Significant-Gravitas/projects/2  
        [discord]: https://discord.gg/autogpt
        [discussions]: https://github.com/Significant-Gravitas/AutoGPT/discussions
        [#tech-support]: https://discord.com/channels/1092243196446249134/1092275629602394184
        [existing issues]: https://github.com/Significant-Gravitas/AutoGPT/issues?q=is%3Aissue
        [wiki page on Contributing]: https://github.com/Significant-Gravitas/AutoGPT/wiki/Contributing

  - type: checkboxes
    attributes:
      label: ⚠️ Search for existing issues first ⚠️
      description: >
        Please [search the history](https://github.com/Significant-Gravitas/AutoGPT/issues)
        to see if an issue already exists for the same problem.
      options:
        - label: I have searched the existing issues, and there is no existing issue for my problem
          required: true

  - type: markdown
    attributes:
      value: |
        Please confirm that the issue you have is described well and precise in the title above ⬆️.
        A good rule of thumb: What would you type if you were searching for the issue?
        
        For example:
        BAD - my AutoGPT keeps looping
        GOOD - After performing execute_python_file, AutoGPT goes into a loop where it keeps trying to execute the file.
        
        ⚠️ SUPER-busy repo, please help the volunteer maintainers.
        The less time we spend here, the more time we can spend building AutoGPT.
        
        Please help us help you by following these steps:
        - Search for existing issues, adding a comment when you have the same or similar issue is tidier than "new issue" and 
          newer issues will not be reviewed earlier, this is dependent on the current priorities set by our wonderful team
        - Ask on our Discord if your issue is known when you are unsure (https://discord.gg/autogpt)
        - Provide relevant info:
          - Provide commit-hash (`git rev-parse HEAD` gets it) if possible
          - If it's a pip/packages issue, mention this in the title and provide pip version, python version
          - If it's a crash, provide traceback and describe the error you got as precise as possible in the title.

  - type: dropdown
    attributes:
      label: Which Operating System are you using?
      description: >
        Please select the operating system you were using to run AutoGPT when this problem occurred.
      options:
        - Windows
        - Linux
        - MacOS
        - Docker
        - Devcontainer / Codespace
        - Windows Subsystem for Linux (WSL)
        - Other
    validations:
      required: true
      nested_fields:
        - type: text
          attributes:
            label: Specify the system
            description: Please specify the system you are working on.

  - type: dropdown
    attributes:
      label: Which version of AutoGPT are you using?
      description: |
        Please select which version of AutoGPT you were using when this issue occurred.
        If you downloaded the code from the [releases page](https://github.com/Significant-Gravitas/AutoGPT/releases/) make sure you were using the latest code. 
        **If you weren't please try with the [latest code](https://github.com/Significant-Gravitas/AutoGPT/releases/)**.
        If installed with git you can run `git branch` to see which version of AutoGPT you are running.
      options:
        - Latest Release
        - Stable (branch)
        - Master (branch)
    validations:
      required: true

  - type: dropdown
    attributes:
      label: What LLM Provider do you use?
      description: >
        If you are using AutoGPT with `SMART_LLM=gpt-3.5-turbo`, your problems may be caused by
        the [limitations](https://github.com/Significant-Gravitas/AutoGPT/issues?q=is%3Aissue+label%3A%22AI+model+limitation%22) of GPT-3.5.
      options:
        - Azure
        - Groq
        - Anthropic
        - Llamafile
        - Other (detail in issue)
    validations:
      required: true

  - type: dropdown
    attributes:
      label: Which area covers your issue best?
      description: >
        Select the area related to the issue you are reporting.
      options:
        - Installation and setup
        - Memory
        - Performance
        - Prompt
        - Commands
        - Plugins
        - AI Model Limitations
        - Challenges
        - Documentation
        - Logging
        - Agents
        - Other
    validations:
      required: true
      autolabels: true
      nested_fields:
        - type: text
          attributes:
            label: Specify the area
            description: Please specify the area you think is best related to the issue.

  - type: input
    attributes:
      label: What commit or version are you using?
      description: It is helpful for us to reproduce to know what version of the software you were using when this happened. Please run `git log -n 1 --pretty=format:"%H"` to output the full commit hash.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Describe your issue.
      description: Describe the problem you are experiencing. Try to describe only the issue and phrase it short but clear. ⚠️ Provide NO other data in this field
    validations:
      required: true

  #Following are optional file content uploads
  - type: markdown
    attributes:
      value: |
        ⚠️The following is OPTIONAL, please keep in mind that the log files may contain personal information such as credentials.⚠️
        
        "The log files are located in the folder 'logs' inside the main AutoGPT folder."

  - type: textarea
    attributes:
      label: Upload Activity Log Content
      description: |
        Upload the activity log content, this can help us understand the issue better. 
        To do this, go to the folder logs in your main AutoGPT folder, open activity.log and copy/paste the contents to this field. 
        ⚠️ The activity log may contain personal data given to AutoGPT by you in prompt or input as well as 
        any personal information that AutoGPT collected out of files during last run. Do not add the activity log if you are not comfortable with sharing it. ⚠️
    validations:
      required: false

  - type: textarea
    attributes:
      label: Upload Error Log Content
      description: |
        Upload the error log content, this will help us understand the issue better. 
        To do this, go to the folder logs in your main AutoGPT folder, open error.log and copy/paste the contents to this field. 
        ⚠️ The error log may contain personal data given to AutoGPT by you in prompt or input as well as 
        any personal information that AutoGPT collected out of files during last run. Do not add the activity log if you are not comfortable with sharing it. ⚠️
    validations:
      required: false
