name: Repo - Github Stats

on:
  schedule:
    # Run this once per day, towards the end of the day for keeping the most
    # recent data point most meaningful (hours are interpreted in UTC).
    - cron: "0 23 * * *"
  workflow_dispatch: # Allow for running this manually.

jobs:
  j1:
    name: github-repo-stats
    runs-on: ubuntu-latest
    steps:
      - name: run-ghrs
        # Use latest release.
        uses: jgehrcke/github-repo-stats@HEAD
        with:
          ghtoken: ${{ secrets.ghrs_github_api_token }}

