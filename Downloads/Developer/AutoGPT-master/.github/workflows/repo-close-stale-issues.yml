name: Repo - Close stale issues
on:
  schedule:
    - cron: '30 1 * * *'
  workflow_dispatch:

permissions:
  issues: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          # operations-per-run: 5000
          stale-issue-message: >
            This issue has automatically been marked as _stale_ because it has not had
            any activity in the last 170 days. You can _unstale_ it by commenting or
            removing the label. Otherwise, this issue will be closed in 10 days.
          stale-pr-message: >
            This pull request has automatically been marked as _stale_ because it has
            not had any activity in the last 50 days. You can _unstale_ it by commenting
            or removing the label.
          close-issue-message: >
            This issue was closed automatically because it has been stale for 10 days
            with no activity.
          days-before-stale: 170
          days-before-close: 10
          # Do not touch meta issues:
          exempt-issue-labels: meta,fridge,project management
          # Do not affect pull requests:
          days-before-pr-stale: -1
          days-before-pr-close: -1
