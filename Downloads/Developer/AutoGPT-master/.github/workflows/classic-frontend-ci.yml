name: Classic - Frontend CI/CD

on:
  push:
    branches:
      - master
      - dev
      - 'ci-test*' # This will match any branch that starts with "ci-test"
    paths:
      - 'classic/frontend/**'
      - '.github/workflows/classic-frontend-ci.yml'
  pull_request:
    paths:
      - 'classic/frontend/**'
      - '.github/workflows/classic-frontend-ci.yml'

jobs:
  build:
    permissions:
      contents: write
      pull-requests: write
    runs-on: ubuntu-latest
    env:
      BUILD_BRANCH: ${{ format('classic-frontend-build/{0}', github.ref_name) }}

    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.13.2'

      - name: Build Flutter to Web
        run: |
          cd classic/frontend
          flutter build web --base-href /app/

      # - name: Commit and Push to ${{ env.BUILD_BRANCH }}
      #   if: github.event_name == 'push'
      #   run: |
      #     git config --local user.email "<EMAIL>"
      #     git config --local user.name "GitHub Action"
      #     git add classic/frontend/build/web
      #     git checkout -B ${{ env.BUILD_BRANCH }}
      #     git commit -m "Update frontend build to ${GITHUB_SHA:0:7}" -a
      #     git push -f origin ${{ env.BUILD_BRANCH }}

      - name: Create PR ${{ env.BUILD_BRANCH }} -> ${{ github.ref_name }}
        if: github.event_name == 'push'
        uses: peter-evans/create-pull-request@v7
        with:
          add-paths: classic/frontend/build/web
          base: ${{ github.ref_name }}
          branch: ${{ env.BUILD_BRANCH }}
          delete-branch: true
          title: "Update frontend build in `${{ github.ref_name }}`"
          body: "This PR updates the frontend build based on commit ${{ github.sha }}."
          commit-message: "Update frontend build based on commit ${{ github.sha }}"
