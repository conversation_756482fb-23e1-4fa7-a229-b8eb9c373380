name: Repo - Enforce dev as base branch
on:
  pull_request_target:
    branches: [ master ]
    types: [ opened ]

jobs:
  check_pr_target:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Check if PR is from dev or hotfix
        if: ${{ !(startsWith(github.event.pull_request.head.ref, 'hotfix/') || github.event.pull_request.head.ref == 'dev') }}
        run: |
          gh pr comment ${{ github.event.number }} --repo "$REPO" \
            --body $'This PR targets the `master` branch but does not come from `dev` or a `hotfix/*` branch.\n\nAutomatically setting the base branch to `dev`.'
          gh pr edit ${{ github.event.number }} --base dev --repo "$REPO"
        env:
          GITHUB_TOKEN: ${{ github.token }}
          REPO: ${{ github.repository }}
