name: AutoGPT Platform - Frontend CI

on:
  push:
    branches: [master, dev]
    paths:
      - ".github/workflows/platform-frontend-ci.yml"
      - "autogpt_platform/frontend/**"
  pull_request:
    paths:
      - ".github/workflows/platform-frontend-ci.yml"
      - "autogpt_platform/frontend/**"
  merge_group:

defaults:
  run:
    shell: bash
    working-directory: autogpt_platform/frontend

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "21"

      - name: Enable corepack
        run: corepack enable

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run lint
        run: pnpm lint

  type-check:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "21"

      - name: Enable corepack
        run: corepack enable

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tsc check
        run: pnpm type-check

  test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, webkit]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "21"

      - name: Enable corepack
        run: corepack enable

      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          large-packages: false # slow
          docker-images: false # limited benefit

      - name: Copy default supabase .env
        run: |
          cp ../.env.example ../.env

      - name: Copy backend .env
        run: |
          cp ../backend/.env.example ../backend/.env

      - name: Run docker compose
        run: |
          docker compose -f ../docker-compose.yml up -d

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup .env
        run: cp .env.example .env

      - name: Build frontend
        run: pnpm build --turbo
        # uses Turbopack, much faster and safe enough for a test pipeline

      - name: Install Browser '${{ matrix.browser }}'
        run: pnpm playwright install --with-deps ${{ matrix.browser }}

      - name: Run Playwright tests
        run: pnpm test:no-build --project=${{ matrix.browser }}

      - name: Print Final Docker Compose logs
        if: always()
        run: docker compose -f ../docker-compose.yml logs

      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: playwright-report-${{ matrix.browser }}
          path: playwright-report/
          retention-days: 30
