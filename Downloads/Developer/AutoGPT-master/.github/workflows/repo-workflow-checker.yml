name: Repo - PR Status Checker
on:
  pull_request:
    types: [opened, synchronize, reopened]
  merge_group:

jobs:
  status-check:
    name: Check PR Status
    runs-on: ubuntu-latest
    steps:
      # - name: Wait some time for all actions to start
      #   run: sleep 30
      - uses: actions/checkout@v4
        # with:
          # fetch-depth: 0
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install requests
      - name: Check PR Status
        run: |
          echo "Current directory before running Python script:"
          pwd
          echo "Attempting to run Python script:"
          python .github/workflows/scripts/check_actions_status.py
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
