name: Classic - Purge Auto-GPT Docker CI cache

on:
  schedule:
    - cron: 20 4 * * 1,4

env:
  BASE_BRANCH: dev
  IMAGE_NAME: auto-gpt

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        build-type: [release, dev]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - id: build
        name: Build image
        uses: docker/build-push-action@v6
        with:
          context: classic/
          file: classic/Dockerfile.autogpt
          build-args: BUILD_TYPE=${{ matrix.build-type }}
          load: true # save to docker images
          # use GHA cache as read-only
          cache-to: type=gha,scope=autogpt-docker-${{ matrix.build-type }},mode=max

      - name: Generate build report
        env:
          event_name: ${{ github.event_name }}
          event_ref: ${{ github.event.schedule }}

          build_type: ${{ matrix.build-type }}

          prod_branch: master
          dev_branch: dev
          repository: ${{ github.repository }}
          base_branch: ${{ github.ref_name != 'master' && github.ref_name != 'dev' && 'dev' || 'master' }}

          current_ref: ${{ github.ref_name }}
          commit_hash: ${{ github.sha }}
          source_url: ${{ format('{0}/tree/{1}', github.event.repository.url, github.sha) }}
          push_forced_label:

          new_commits_json: ${{ null }}
          compare_url_template: ${{ format('/{0}/compare/{{base}}...{{head}}', github.repository) }}

          github_context_json: ${{ toJSON(github) }}
          job_env_json: ${{ toJSON(env) }}
          vars_json: ${{ toJSON(vars) }}

        run: .github/workflows/scripts/docker-ci-summary.sh >> $GITHUB_STEP_SUMMARY
        continue-on-error: true
